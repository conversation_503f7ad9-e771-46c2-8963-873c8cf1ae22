#!/usr/bin/env python3
"""data_receiver_server.py

TCP server that receives binary tool data from a client and provides
statistics, validation, and optional data logging.

This server is designed to work with the modified replay_socket_client.py
(converted from replay_socket_server.py) to test data transmission.

Key features:
• Receives 16-byte samples continuously from connected clients
• Validates data format and provides real-time statistics
• Optional data logging to file for analysis
• Configurable connection handling and logging levels
• Graceful handling of client disconnections

Usage
-----
python data_receiver_server.py [--host 0.0.0.0] [--port 1333] \
                               [--log-data] [--output-file received_data.dat] \
                               [--stats-interval 5.0] [--debug]

Practical notes
---------------
The server expects 16-byte samples as sent by the original replay_socket_server.py.
Statistics are printed at regular intervals showing throughput and data validation.
"""
from __future__ import annotations

import argparse
import pathlib
import socket
import time
import threading
import logging
import sys
from typing import Optional, BinaryIO
from dataclasses import dataclass, field

try:
    import colorlog
    HAS_COLORLOG = True
except ImportError:
    HAS_COLORLOG = False

BYTES_PER_SAMPLE = 16
DEFAULT_STATS_INTERVAL = 5.0  # seconds


def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """Setup a logger with colored output if colorlog is available."""
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Avoid adding multiple handlers if logger already exists
    if logger.handlers:
        return logger

    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(level)

    # Setup formatter with colors if available
    if HAS_COLORLOG:
        formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

    handler.setFormatter(formatter)
    logger.addHandler(handler)

    return logger


# Initialize logger
logger = setup_logger('data_receiver_server')


@dataclass
class ConnectionStats:
    """Statistics for a client connection."""
    start_time: float = field(default_factory=time.time)
    bytes_received: int = 0
    samples_received: int = 0
    last_stats_time: float = field(default_factory=time.time)
    last_bytes_received: int = 0
    invalid_data_count: int = 0

    @property
    def total_duration(self) -> float:
        return time.time() - self.start_time

    @property
    def average_throughput_bps(self) -> float:
        if self.total_duration > 0:
            return self.bytes_received / self.total_duration
        return 0.0

    @property
    def average_sample_rate(self) -> float:
        if self.total_duration > 0:
            return self.samples_received / self.total_duration
        return 0.0

    def get_interval_stats(self, interval: float) -> tuple[float, float]:
        """Get throughput and sample rate for the last interval."""
        current_time = time.time()
        time_diff = current_time - self.last_stats_time
        bytes_diff = self.bytes_received - self.last_bytes_received
        
        if time_diff > 0:
            interval_bps = bytes_diff / time_diff
            interval_sps = (bytes_diff / BYTES_PER_SAMPLE) / time_diff
        else:
            interval_bps = interval_sps = 0.0
        
        # Update for next interval
        self.last_stats_time = current_time
        self.last_bytes_received = self.bytes_received
        
        return interval_bps, interval_sps


class DataReceiverServer:
    """TCP server for receiving binary tool data."""
    
    def __init__(
        self,
        host: str,
        port: int,
        log_data: bool = False,
        output_file: Optional[pathlib.Path] = None,
        stats_interval: float = DEFAULT_STATS_INTERVAL,
    ):
        self.host = host
        self.port = port
        self.log_data = log_data
        self.output_file = output_file
        self.stats_interval = stats_interval
        self.running = False
        self.stats_thread: Optional[threading.Thread] = None
        self.output_fp: Optional[BinaryIO] = None

    def start(self) -> None:
        """Start the server."""
        addr = (self.host, self.port)
        logger.info(f"Starting data receiver server on {addr}")
        
        if self.log_data and self.output_file:
            logger.info(f"Data logging enabled, output file: {self.output_file}")
            self.output_fp = self.output_file.open("wb")

        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as server_sock:
            server_sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_sock.bind(addr)
            server_sock.listen(1)
            self.running = True
            
            logger.info(f"Server listening on {addr}, waiting for clients...")
            
            try:
                while self.running:
                    try:
                        conn, remote = server_sock.accept()
                        logger.info(f"Client connected from {remote}")
                        self._handle_client(conn, remote)
                    except socket.error as e:
                        if self.running:
                            logger.error(f"Socket error: {e}")
                        break
            except KeyboardInterrupt:
                logger.info("Received interrupt signal, shutting down...")
            finally:
                self.running = False
                if self.output_fp:
                    self.output_fp.close()
                    logger.info(f"Closed output file: {self.output_file}")

    def _handle_client(self, conn: socket.socket, remote: tuple) -> None:
        """Handle a single client connection."""
        stats = ConnectionStats()
        
        # Start stats reporting thread
        self.stats_thread = threading.Thread(
            target=self._stats_reporter,
            args=(stats, remote),
            daemon=True
        )
        self.stats_thread.start()
        
        try:
            # Enable TCP keep-alive
            conn.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            
            # Set socket timeout for receive operations
            conn.settimeout(30.0)  # 30 second timeout
            
            buffer = bytearray()
            
            while self.running:
                try:
                    data = conn.recv(4096)  # Receive up to 4KB at a time
                    if not data:
                        logger.info(f"Client {remote} closed connection")
                        break
                    
                    buffer.extend(data)
                    stats.bytes_received += len(data)
                    
                    # Process complete samples
                    while len(buffer) >= BYTES_PER_SAMPLE:
                        sample = bytes(buffer[:BYTES_PER_SAMPLE])
                        buffer = buffer[BYTES_PER_SAMPLE:]
                        
                        if self._validate_sample(sample):
                            stats.samples_received += 1
                            if self.output_fp:
                                self.output_fp.write(sample)
                        else:
                            stats.invalid_data_count += 1
                            logger.warning(f"Invalid sample received from {remote}")
                
                except socket.timeout:
                    logger.warning(f"Timeout waiting for data from {remote}")
                    continue
                except socket.error as e:
                    logger.error(f"Socket error with client {remote}: {e}")
                    break
                    
        finally:
            conn.close()
            self._print_final_stats(stats, remote)
            logger.info(f"Client {remote} disconnected")

    def _validate_sample(self, sample: bytes) -> bool:
        """Validate that a sample is the correct format."""
        # Basic validation: check length
        if len(sample) != BYTES_PER_SAMPLE:
            return False
        
        # Additional validation could be added here
        # For example, checking for reasonable value ranges
        
        return True

    def _stats_reporter(self, stats: ConnectionStats, remote: tuple) -> None:
        """Report statistics at regular intervals."""
        while self.running:
            time.sleep(self.stats_interval)
            if not self.running:
                break
                
            interval_bps, interval_sps = stats.get_interval_stats(self.stats_interval)
            
            logger.info(
                f"Client {remote} - "
                f"Samples: {stats.samples_received:,}, "
                f"Bytes: {stats.bytes_received:,}, "
                f"Rate: {interval_sps:.1f} sps, "
                f"Throughput: {interval_bps/1024:.1f} KB/s, "
                f"Invalid: {stats.invalid_data_count}"
            )

    def _print_final_stats(self, stats: ConnectionStats, remote: tuple) -> None:
        """Print final statistics for a client connection."""
        logger.info(
            f"Final stats for {remote} - "
            f"Duration: {stats.total_duration:.1f}s, "
            f"Total samples: {stats.samples_received:,}, "
            f"Total bytes: {stats.bytes_received:,}, "
            f"Avg rate: {stats.average_sample_rate:.1f} sps, "
            f"Avg throughput: {stats.average_throughput_bps/1024:.1f} KB/s, "
            f"Invalid samples: {stats.invalid_data_count}"
        )


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    p = argparse.ArgumentParser(description="TCP server for receiving binary tool data")
    p.add_argument("--host", default="0.0.0.0", help="listen interface (default 0.0.0.0)")
    p.add_argument("--port", type=int, default=1333, help="TCP port to listen on (default 1333)")
    p.add_argument("--log-data", action="store_true", help="log received data to file")
    p.add_argument(
        "--output-file",
        type=pathlib.Path,
        default="received_data.dat",
        help="output file for logged data (default received_data.dat)"
    )
    p.add_argument(
        "--stats-interval",
        type=float,
        default=DEFAULT_STATS_INTERVAL,
        help="statistics reporting interval in seconds (default 5.0)"
    )
    p.add_argument("--debug", action="store_true", help="enable debug logging")
    return p.parse_args()


def main() -> None:
    """Main entry point."""
    args = parse_args()
    
    # Set log level based on debug flag
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    server = DataReceiverServer(
        host=args.host,
        port=args.port,
        log_data=args.log_data,
        output_file=args.output_file if args.log_data else None,
        stats_interval=args.stats_interval,
    )
    
    try:
        server.start()
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise


if __name__ == "__main__":
    main()
