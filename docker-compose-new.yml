services:
  # Data receiver server - receives data from client
  data-receiver:
    image: docker-mock-services:latest
    container_name: data-receiver-server
    environment:
      - PYTHONUNBUFFERED=1
    command: >
      python -u data_receiver_server.py 
        --host 0.0.0.0 
        --port 1333 
        --log-data 
        --output-file /app/received_data.dat 
        --stats-interval 5.0
    ports:
      - "1333:1333"   # Data receiver port -> host:1333
    volumes:
      - ./received_data:/app/received_data:rw  # Mount for output data
    restart: unless-stopped

  # Mock AP server (unchanged)
  mock-ap:
    image: docker-mock-services:latest
    container_name: mock-ap-server
    environment:
      - PYTHONUNBUFFERED=1
    command: >
      python -u mock_ap_server.py --host 0.0.0.0 --port 8000
    ports:
      - "8000:8000"   # AP HTTP interface -> host:8000
    restart: unless-stopped

  # Data client - sends data to receiver (optional, can be run manually)
  data-client:
    image: docker-mock-services:latest
    container_name: data-client
    environment:
      - PYTHONUNBUFFERED=1
    command: >
      python -u replay_socket_client.py 
        samples/sample_1.dat 
        --host data-receiver 
        --port 1333 
        --sample-rate 10000 
        --block-samples 100 
        --retry-interval 5 
        --max-retries 0
    volumes:
      - ./samples:/app/samples:ro
    depends_on:
      - data-receiver
    restart: "no"  # Don't auto-restart, run manually when needed

# Create volume for received data
volumes:
  received_data:
