#!/usr/bin/env python3
"""replay_socket_server.py

TCP server that replays a `.dat`/`.bin` stream produced by
`convert_msra_to_bin.py` so that a client (`SocketWorker` in this project)
receives data *sample-by-sample* at (approximately) the original sample rate.

Key differences from the first version:
• Data are **not** bundled into large "chunks"; we transmit continuously in
  small blocks so the socket remains busy and the client can `recv()` any
  amount it wants.
• Throttling is based on *sample rate* (default 10 kHz).  By default we send
  `BLOCK_SAMPLES` (=100) samples per write and then sleep for
  `BLOCK_SAMPLES / sample_rate` seconds.
• CLI flags let you tune sample-rate and block-size or disable throttling for
  maximum throughput.

Usage
-----
python replay_socket_server.py sample_1.dat [--host 0.0.0.0] [--port 1333] \
                                           [--sample-rate 10000] \
                                           [--block-samples 100] \
                                           [--no-throttle]

Practical notes
---------------
Sending a `sendall()` every 100 µs (true 10 kHz) is unrealistic in CPython, so
we default to **100 samples per block** (≈10 ms) which is still far below the
GUI’s chunk size yet gentle on the OS scheduler.  Adjust as needed.
"""
from __future__ import annotations

import argparse
import pathlib
import socket
import time
from typing import List

BYTES_PER_SAMPLE = 16
DEFAULT_SAMPLE_RATE = 10_000  # Hz
DEFAULT_BLOCK_SAMPLES = 100   # 100×16 = 1 600 B per send, ~10 ms at 10 kHz


def serve(
    data_path: pathlib.Path,
    host: str,
    port: int,
    sample_rate: float,
    block_samples: int,
    throttle: bool,
) -> None:
    """Start the replay server."""
    addr = (host, port)
    print(f"[replay] Listening on {addr}, serving {data_path}…")

    data = data_path.read_bytes()
    if len(data) % BYTES_PER_SAMPLE:
        raise ValueError("Data length is not a multiple of 16-byte samples")

    total_samples = len(data) // BYTES_PER_SAMPLE
    print(f"[replay] Total samples: {total_samples:,}")

    block_bytes = block_samples * BYTES_PER_SAMPLE
    blocks: List[memoryview] = [
        memoryview(data[i : i + block_bytes])
        for i in range(0, len(data), block_bytes)
    ]
    print(
        f"[replay] Prepared {len(blocks)} blocks of {block_samples} samples "
        f"({block_bytes} bytes)"
    )

    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as server_sock:
        server_sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_sock.bind(addr)
        server_sock.listen(1)
        while True:
            print("[replay] Waiting for client…")
            conn, remote = server_sock.accept()
            print(f"[replay] Client connected from {remote}")
            # Enable TCP keep-alive so that idle connections are detected and
            # Linux sends probes; helps Docker/NAT setups stay open.
            try:
                conn.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                # Platform-specific tuning – ignore if not available.
                if hasattr(socket, "TCP_KEEPIDLE"):
                    conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 60)
                if hasattr(socket, "TCP_KEEPINTVL"):
                    conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 10)
                if hasattr(socket, "TCP_KEEPCNT"):
                    conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)
            except OSError:
                pass  # keep going even if keep-alive can't be set

            try:
                _stream(conn, blocks, sample_rate, block_samples, throttle)
            finally:
                conn.close()
                print(f"[replay] Client {remote} disconnected")


def _stream(
    conn: socket.socket,
    blocks: List[memoryview],
    sample_rate: float,
    block_samples: int,
    throttle: bool,
) -> None:
    """Send the blocks; optionally throttle to real-time speed."""
    sleep_time = (block_samples / sample_rate) if throttle else 0.0
    try:
        while True:
            for blk in blocks:
                conn.sendall(blk)
                if sleep_time:
                    time.sleep(sleep_time)
    except (BrokenPipeError, ConnectionResetError):
        # Client closed the connection – simply return to let caller handle log.
        return


# ---------------------------------------------------------------------------
# CLI helpers
# ---------------------------------------------------------------------------

def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(description="Replay binary tool data over TCP in real-time")
    p.add_argument("data", type=pathlib.Path, help=".dat/.bin file generated by convert_msra_to_bin.py")
    p.add_argument("--host", default="0.0.0.0", help="listen interface (default 0.0.0.0)")
    p.add_argument("--port", type=int, default=1333, help="TCP port to listen on")
    p.add_argument("--sample-rate", type=float, default=DEFAULT_SAMPLE_RATE, help="samples per second (default 10000)")
    p.add_argument(
        "--block-samples",
        type=int,
        default=DEFAULT_BLOCK_SAMPLES,
        help="number of samples per send() call (default 100)",
    )
    p.add_argument("--no-throttle", action="store_true", help="send data as fast as possible")
    return p.parse_args()


def main() -> None:
    args = parse_args()
    serve(
        data_path=args.data,
        host=args.host,
        port=args.port,
        sample_rate=args.sample_rate,
        block_samples=args.block_samples,
        throttle=not args.no_throttle,
    )


if __name__ == "__main__":
    main()
