import sys
import random
from PySide2.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QSlider
from PySide2.QtGui import QPainter, QPen, QBrush
from PySide2.QtCore import Qt, QTimer, QPropertyAnimation, Property


class BalanceWidget(QWidget):
    def __init__(self):
        super().__init__()
        self._balance_value = 0.0  # -1.0 ~ 1.0
        self.setMinimumSize(400, 200)

    def getBalanceValue(self):
        return self._balance_value

    def setBalanceValue(self, value):
        self._balance_value = value
        self.update()

    balanceValue = Property(float, getBalanceValue, setBalanceValue)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        w = self.width()
        h = self.height()
        centerX, centerY = w // 2, h // 2

        # ---- 傾斜角度 ----
        max_angle = 15  # 最大傾斜角度
        angle = self._balance_value * max_angle

        painter.translate(centerX, centerY)  # 移動畫布中心
        painter.rotate(angle)  # 旋轉基準線

        # 畫水平基準線
        painter.setPen(QPen(Qt.gray, 3))
        painter.drawLine(-w // 2, 0, w // 2, 0)

        # ---- 畫圓球 (不跟著旋轉，所以要還原旋轉) ----
        painter.rotate(-angle)
        painter.translate(-centerX, -centerY)

        offsetX = self._balance_value * (w // 2 - 40)
        x = centerX + offsetX
        y = centerY

        painter.setBrush(QBrush(Qt.red if self._balance_value > 0 else Qt.blue))
        painter.drawEllipse(int(x - 20), int(y - 20), 40, 40)


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("水平動態平衡 (基準線可傾斜)")

        self.balance_widget = BalanceWidget()

        # 控制 Slider
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(-100, 100)
        self.slider.setValue(0)
        self.slider.valueChanged.connect(self.animateBalance)

        # 自動模擬按鈕
        self.button = QPushButton("自動模擬")
        self.button.setCheckable(True)
        self.button.clicked.connect(self.toggleSimulation)

        layout = QVBoxLayout()
        layout.addWidget(self.balance_widget)
        layout.addWidget(self.slider)
        layout.addWidget(self.button)
        self.setLayout(layout)

        # Timer for simulation
        self.timer = QTimer()
        self.timer.timeout.connect(self.simulate)

    def animateBalance(self, value):
        target = value / 100.0
        animation = QPropertyAnimation(self.balance_widget, b"balanceValue")
        animation.setDuration(400)
        animation.setStartValue(self.balance_widget.getBalanceValue())
        animation.setEndValue(target)
        animation.start()
        self.current_animation = animation  # 避免被回收

    def toggleSimulation(self, checked):
        if checked:
            self.timer.start(800)
            self.button.setText("停止模擬")
        else:
            self.timer.stop()
            self.button.setText("自動模擬")

    def simulate(self):
        value = random.randint(-100, 100)
        self.slider.setValue(value)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
