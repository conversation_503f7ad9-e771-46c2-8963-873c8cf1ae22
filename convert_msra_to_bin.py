#!/usr/bin/env python3
"""convert_msra_to_bin.py

Utility to regenerate the original 16-byte sensor frames that were fed into
`SockerWorker.receive_chunk()` from an exported `.msra` file and its
corresponding `sample_tool_x_info` calibration file.

The program streams the input file so it can handle millions of lines with
constant memory usage.  It writes a binary `.dat`/`.bin` file containing the
concatenated 16-byte samples, ready to be replayed over a socket.

Usage
-----
python convert_msra_to_bin.py <sample.msra> <sample_tool_info> <output.dat>

"""
from __future__ import annotations

import sys
import math
import pathlib
from typing import Dict, Tuple, Iterable, BinaryIO


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------

def round_half_up(value: float) -> int:
    """Round *half up* to the nearest integer.

    Python's built-in round() implements bankers rounding (half-to-even) which
    is not what we want.  This helper mimics the behaviour most humans expect:
    0.5 → 1, ‑0.5 → ‑0.
    """
    if value >= 0:
        return int(math.floor(value + 0.5))
    else:
        return int(math.ceil(value - 0.5))


def clamp(value: int, lo: int, hi: int) -> int:
    """Clamp *value* into the inclusive range [lo, hi]."""
    return max(lo, min(hi, value))


# ---------------------------------------------------------------------------
# Parsing of the tool-info (calibration) file
# ---------------------------------------------------------------------------

_EXPECTED_KEYS = {
    "tare_bx",
    "tare_by",
    "tare_bz",
    "tare_bt",
    "tare_gx",
    "tare_gy",
    "tare_gz",
    "linear_x",
    "linear_y",
    "linear_z",
    "linear_t",
    "lc",
    "hl",
    "kl",
}


def parse_tool_info(path: pathlib.Path) -> Dict[str, float]:
    """Return a dictionary of calibration constants from *path*.

    The file contains CSV lines like `key, value`.  Extra keys are ignored; we
    only keep those we expect.  An error is raised if any expected key is
    missing.
    """
    constants: Dict[str, float] = {}
    with path.open("rt", encoding="utf-8") as fp:
        for raw in fp:
            if not raw.strip():
                continue
            parts = [p.strip() for p in raw.split(",", 1)]
            if len(parts) != 2:
                continue  # skip malformed
            key, value = parts[0].lower(), parts[1]
            if key in _EXPECTED_KEYS:
                try:
                    constants[key] = float(value)
                except ValueError:
                    raise ValueError(f"Could not parse float for {key!r} in {path}")

    missing = _EXPECTED_KEYS - constants.keys()
    if missing:
        missing_str = ", ".join(sorted(missing))
        raise ValueError(f"Tool-info file {path} is missing keys: {missing_str}")

    # Pre-compute parameters used for bending-force inverse calculation.
    param_h_l = constants["lc"] / (constants["hl"] + constants["kl"])
    constants["param_n_x"] = constants["linear_x"] * param_h_l
    constants["param_n_y"] = constants["linear_y"] * param_h_l
    constants["param_n_z"] = constants["linear_z"]  # unchanged
    constants["param_n_t"] = constants["linear_t"]  # unchanged
    return constants


# ---------------------------------------------------------------------------
# Accelerometer reconstruction
# ---------------------------------------------------------------------------

def accel_line_to_bytes(fields: Iterable[str], calib: Dict[str, float]) -> bytes:
    """Convert an accelerometer data line (split into *fields*) to 16 bytes."""
    try:
        gx, gy, gz, temp, rssi = map(float, fields)
    except ValueError as e:
        raise ValueError(f"Cannot parse accelerometer fields {fields}: {e}")

    # Undo tare offset.
    raw_x = gx + calib["tare_gx"]
    raw_y = gy + calib["tare_gy"]
    raw_z = gz + calib["tare_gz"]
    raw_temp = temp  # no conversion needed besides byte swap later
    raw_rssi = rssi  # already an integer typically

    # Convert to unsigned integers.
    int_x = clamp(round_half_up(raw_x), 0, 0xFFFF)
    int_y = clamp(round_half_up(raw_y), 0, 0xFFFF)
    int_z = clamp(round_half_up(raw_z), 0, 0xFFFF)
    int_temp = clamp(round_half_up(raw_temp), 0, 0xFFFF)
    int_rssi = clamp(round_half_up(raw_rssi), 0, 0xFF)

    # Assemble bytes: X Y Z (big-endian), Temp (little-endian), RSSI, padding.
    payload = bytearray(16)
    payload[0:2] = int_x.to_bytes(2, "big")
    payload[2:4] = int_y.to_bytes(2, "big")
    payload[4:6] = int_z.to_bytes(2, "big")
    payload[6:8] = int_temp.to_bytes(2, "little")  # byte-swapped!
    payload[8] = int_rssi & 0xFF
    # The remaining bytes (9-15) are already zero.
    return bytes(payload)


# ---------------------------------------------------------------------------
# Bending-force reconstruction
# ---------------------------------------------------------------------------

def bending_line_to_bytes(fields: Iterable[str], calib: Dict[str, float]) -> bytes:
    """Convert a bending-force data line (split into *fields*) to 16 bytes."""
    try:
        bx, by, bz, bt, battery = map(float, fields)
    except ValueError as e:
        raise ValueError(f"Cannot parse bending-force fields {fields}: {e}")

    # Undo calibration scaling.
    data_x = (bx / calib["param_n_x"]) + calib["tare_bx"]
    data_y = (by / calib["param_n_y"]) + calib["tare_by"]
    data_ten = (bz / calib["param_n_z"]) + calib["tare_bz"]
    data_tor = (bt / calib["param_n_t"]) + calib["tare_bt"]
    data_bat = battery  # forward path divided by 65535.5 only, no tare

    # Convert to raw 16-bit integers.
    # NOTE: Original decoder divides by 6553.5 (not 65535.5) so we use the
    # reciprocal here to rebuild the raw 16-bit value.
    scale = 6553.5
    int_x = clamp(round_half_up(data_x * scale), 0, 0xFFFF)
    int_y = clamp(round_half_up(data_y * scale), 0, 0xFFFF)
    int_ten = clamp(round_half_up(data_ten * scale), 0, 0xFFFF)
    int_tor = clamp(round_half_up(data_tor * scale), 0, 0xFFFF)
    int_bat = clamp(round_half_up(data_bat * scale), 0, 0xFFFF)

    # Assemble bytes: five 16-bit words big-endian, then padding.
    payload = bytearray(16)
    payload[0:2] = int_x.to_bytes(2, "big")
    payload[2:4] = int_y.to_bytes(2, "big")
    payload[4:6] = int_ten.to_bytes(2, "big")
    payload[6:8] = int_tor.to_bytes(2, "big")
    payload[8:10] = int_bat.to_bytes(2, "big")
    # Remaining bytes 10-15 are zero by default.
    return bytes(payload)


# ---------------------------------------------------------------------------
# Main streaming loop
# ---------------------------------------------------------------------------

def process_msra(msra_path: pathlib.Path, calib: Dict[str, float], out_fp: BinaryIO) -> None:
    """Stream *msra_path* and write reconstructed samples to *out_fp*."""
    with msra_path.open("rt", encoding="utf-8", errors="ignore") as fp:
        for idx, raw in enumerate(fp):
            raw = raw.strip()
            if not raw:
                continue  # skip empty lines
            if idx == 0 and raw.startswith("!"):
                # Metadata line – already handled via tool-info file.
                continue

            if raw.startswith("*"):
                # Accelerometer sample.
                parts = [p.strip() for p in raw.split(",")][1:]  # drop '*'
                sample_bytes = accel_line_to_bytes(parts, calib)
            else:
                parts = [p.strip() for p in raw.split(",")]
                sample_bytes = bending_line_to_bytes(parts, calib)

            out_fp.write(sample_bytes)


# ---------------------------------------------------------------------------
# Entry-point wrapper
# ---------------------------------------------------------------------------

def main(argv: list[str] | None = None) -> None:
    argv = sys.argv[1:] if argv is None else argv
    if len(argv) != 3:
        prog = pathlib.Path(sys.argv[0]).name
        print(
            f"Usage: {prog} <input.msra> <tool_info.txt> <output.bin>",
            file=sys.stderr,
        )
        sys.exit(1)

    msra_file = pathlib.Path(argv[0])
    tool_info_file = pathlib.Path(argv[1])
    output_file = pathlib.Path(argv[2])

    if not msra_file.is_file():
        sys.exit(f"Input MSRA file not found: {msra_file}")
    if not tool_info_file.is_file():
        sys.exit(f"Tool-info file not found: {tool_info_file}")

    calib = parse_tool_info(tool_info_file)

    # Try to override Lc/Hl/Kl from the first metadata line of the MSRA file
    try:
        with msra_file.open("rt", encoding="utf-8", errors="ignore") as fp:
            first_line = fp.readline().strip()
        if first_line.startswith("!"):
            parts = first_line.split(",")
            if len(parts) > 24:
                lc, hl, kl = map(float, (parts[22], parts[23], parts[24]))
                if any(math.isfinite(x) for x in (lc, hl, kl)):
                    calib["lc"], calib["hl"], calib["kl"] = lc, hl, kl
                    param_h_l = lc / (hl + kl)
                    calib["param_n_x"] = calib["linear_x"] * param_h_l
                    calib["param_n_y"] = calib["linear_y"] * param_h_l
                    # param_n_z/t unchanged
    except Exception:
        pass  # If anything goes wrong, we fall back to tool-info values

    # Stream processing – open output for binary write.
    with output_file.open("wb") as out_fp:
        process_msra(msra_file, calib, out_fp)

    print(f"Wrote {output_file.stat().st_size} bytes to {output_file}")


if __name__ == "__main__":
    main()

