# .msra Overview

<!-- ## Tool Data
Some data are used in the calculation of bending force and accelerometer data.

(Before MVC)
| index | description | example data |
| - | - | - |
| 0 | tool name | default_************* |
| 1 | tool ip | ************* |
| 2 | sample rate | 10000 |
| 4 | tare_bx | 2.5 | 
| 5 | tare_by | 2.5 |
| 6 | tare_bz | 2.5 |
| 7 | tare_bt | 2.5 |
| 8 | linear_x | 1446 |
| 9 | linear_y | 1465 | 
| 10 | linear_z | 31094 | 
| 11 | linear_t | 1 | 
| 12 | auto_recording | 0 |
| 13 | auto_cf | 0 |
| 14 | auto_fz | 0 |
| 15 | auto_torque | 0 |
| 16 | lc | 0.136 |
| 17 | hl | 0.126 | 
| 19 | kl | 0.04 |
| 19 | bx | 1.1 |
| 20 | by | -1.04 | 
| 21 | bz | 3.64 |
| 22 | bt | 0.0 |

*Note: index 3 is reserved for future use.  -->



## Data Collection
```
SockerWorker.receive_chunk(sample_N, sample_byte)
```
### Parameters
- SampleRate: number of *bending force* samples per second
- SamplePoint1: number of *bending force* samples per chunk
- sample_N: number of *total (bending force and accelerometer)* samples per chunk 
- sample_byte: number of *total (bending force and accelerometer)* byes per chunk

### Current Configuration
```python
SampleRate = 10000
SamplePoint1 = math.ceil(SampleRate / 12.5) # 12.5 chunks per second
sample_N = math.ceil(SamplePoint1 * 1.005) # sample rate of accelerometer is 0.005x that of bending force
sample_byte: int(self.sample_N * 16) # 16 bytes per sample
```

### Conversion to Hex String
A chunk of samples is received in a ``receive_chunk`` call, with 16 bytes per sample.
Each byte in a sample is stored as a hex string. 1 byte = 8 digits in binary = 2 digits in hex. Thus, each sample is eventually stored as a hex string of length 16 * 2 = 32 digits.

### Bending Force vs Accelerometer
The samples inside a chunk are organized as follows:  
The first sample is accelerometer data. Since the sample rate of accelerometer data is 0.005x the sample rate of bending force data, it appears once every 200 samples. That is to say, they are at indices 0, 201, 402, ... etc.  
The rest of the samples are bending force data. They are at indices 1 to 200, 202 to 401, ... etc.

## Calculation
```python
socket_decoder(hex_data)
```
A hex string representing a sample is decoded into "raw data", and the raw data undergoes calculation, converted into "force data" or "adxl (accelerometer) data".  
Currently, the final converted data is rounded to 3 decimal places.  

Only calculations pertaining to data in the msra file are discussed here.

### Accelerometer

#### Decoding
- ``Data_ADXL_X = int(hex_data[0+offset: 4+offset], 16)``
- ``Data_ADXL_Y = int(hex_data[4+offset: 8+offset], 16)``
- ``Data_ADXL_Z = int(hex_data[8+offset: 12+offset],16)``
- ``Data_Temp = int((hex_data[14+offset: 16+offset] + hex_data[12+offset: 14+offset]), 16)``
- ``Wifi_RSSI = int(hex_data[16+offset: 18+offset], 16)``

#### Conversion
- ``gx = Data_ADXL_X - tare_gx``
- ``gy = Data_ADXL_Y - tare_gy``
- ``gz = Data_ADXL_Z - tare_gz``
- ``Data_Temp``: no conversion needed
- ``Wifi_RSSI``: no conversion needed

### Bending Force

#### Decoding
- ``Data_x = int(hex_data[0+offset: 4+offset], 16) / 65535.5``
- ``Data_y = int(hex_data[4+offset: 8+offset], 16) / 65535.5``
- ``Data_ten = int(hex_data[8+offset: 12+offset],16) / 65535.5``
- ``Data_tor = int(hex_data[12+offset: 16+offset], 16) / 65535.5``
- ``Data_battery = int(hex_data[16+offset: 20+offset], 16) / 65535.5``

#### Parameters
- ``param_H_L = Lc / (Hl + Kl)``
- ``param_N_X = linear_x * param_H_L``
- ``param_N_Y = linear_y * param_H_L``
- ``param_N_Z = linear_z``
- ``param_N_T = linear_t``

#### Conversion
- ``bending_x = (Data_x - tare_bx) * param_N_X``
- ``bending_y = (Data_y - tare_by) * param_N_Y``
- ``bending_z = (Data_ten - tare_bz) * param_N_Z``
- ``bending_t = (Data_tor - tare_bt) * param_N_T``
- ``Data_battery``: no conversion needed

## .msra file format
The first line is some information about the tool. Then, each line represents one sample of data.  
The order of samples follow the rules in [this section](#bending-force-vs-accelerometer): accelerometer data is in the second line of the file (i.e. first line of data) and appears every 200 lines; the rest is all bending force data.  
A line of accelerometer data has 6 fields. The first field is a marker '*'. The following fields 2 to 6 are gx, gy, gz, Data_Temp and Wifi_RSSI respectively.  
A line of bending force data has 5 fields, whice are bending_x, bending_y, bending_z, bending_t and Data_battery respectively.

This is how it would look:
```
!,10000,2.594829,3.372119,2.779335,2.191662,1926.0,1905.5,1819.75,-1,0,0,0,0,0,0,0,0,0,0,0,-1,0.136,0.126,0.04,1.1,-1.04,3.64,0.0
*,-7.000,-7.500,-7.750,31.00,49
1.588605, -1.499042, -6.495413, 13.873000, 4.172
0.142447, -1.132752, -11.240053, 13.871000, 4.172
1.407835, 1.248133, -11.240053, 13.880000, 4.172
1.769375, 0.332408, 12.483149, 13.873000, 4.172
1.227065, 0.881843, -1.750772, 13.873000, 4.172
0.323216, 0.515553, 7.738508, 13.874000, 4.172
... (more data)
```
