# Mock Tool
A fake AP and fake tool hosted in a Docker container to facilitate RealTime development, debugging and testing.
There is also a script that can reverse-encode a given .msra file into (approximately) the original binary data emitted by the tool.

## Architecture

This project now supports two different architectures:

### Original Architecture (Server Mode)
- `replay_socket_server.py` acts as a **server** that sends data
- RealTime application acts as a **client** that receives data
- Use `docker-compose.yml` for this setup

### New Architecture (Client Mode)
- `replay_socket_client.py` acts as a **client** that sends data
- `data_receiver_server.py` acts as a **server** that receives and validates data
- Use `docker-compose-new.yml` for this setup
- Useful for testing data transmission and validation

## Environment Setup
### Python 3.8.10
```
pip install -r requirements.txt
```
### Docker

## Test Data
- sample_1.msra = \\DS923-01\ms_rd\RD_13_MachSensing_切削資料\正修-展品\TEST_A182_20250328-155757_20250328-155846第五次精銑一半G02G03S15000F3480.msra
- sample_2.msra = \machsync_onedrive\RD03_Software\Overview-to-MachRadar-file\Before mvc\.msra-file\MachRadar RealTime Pro\Record_20250509-164131.msra ("$" to mark tare are removed for easy parsing)
- sample_3.msra = \\DS923-01\ms_rd\RD_13_MachSensing_切削資料\Record_20250509正修實測\實切_5_A132_20250509-140706_20250509-141207.msra

## Generate Binary Data from .msra
Current .msra files lack some information (linear, etc) required for re-encoding. Temporary solution: the complete information of a tool is stored in sample_x_tool_info. 
```bash
python convert_msra_to_bin.py [msra file] [tool info file] [output file]
```

## Usage

### Original Architecture (Server Mode)
Use this setup to test with the RealTime application as a client.

#### Point RealTime to Fake AP
In the ``.env`` file of the RealTime project, change the network device url to:
```txt
NETWORK_DEVICE_URL=http://127.0.0.1:8000
```

#### Run Container
```bash
docker compose up --build
```

#### Add Fake Tool
In the RealTime GUI, add the fake tool by entering its MAC ```AA:BB:CC:DD:EE:FF```.

#### Connect to Tool
Proceed as usual.

### New Architecture (Client Mode)
Use this setup to test data transmission and validation.

#### Run Data Receiver Server
```bash
# Start the data receiver server and mock AP
docker compose -f docker-compose-new.yml up --build data-receiver mock-ap
```

#### Send Data with Client
```bash
# Option 1: Run client in container
docker compose -f docker-compose-new.yml up data-client

# Option 2: Run client manually (requires Python environment)
python replay_socket_client.py samples/sample_1.dat --host 127.0.0.1 --port 1333

# Option 3: Run with custom parameters
python replay_socket_client.py samples/sample_1.dat \
    --host 127.0.0.1 \
    --port 1333 \
    --sample-rate 10000 \
    --block-samples 100 \
    --no-throttle \
    --debug
```

#### Monitor Data Reception
The data receiver server will log statistics about received data:
- Sample rate and throughput
- Data validation results
- Connection status
- Optional data logging to file

### Manual Testing
You can also run the components manually:

```bash
# Terminal 1: Start data receiver server
python data_receiver_server.py --host 0.0.0.0 --port 1333 --log-data --debug

# Terminal 2: Start data client
python replay_socket_client.py samples/sample_1.dat --host 127.0.0.1 --port 1333 --debug

# Terminal 3: Start mock AP (if needed)
python mock_ap_server.py --host 0.0.0.0 --port 8000
```