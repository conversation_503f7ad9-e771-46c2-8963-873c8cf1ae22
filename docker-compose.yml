services:
  mock-service:
    image: docker-mock-services:latest
    container_name: mock-devices
    environment:
      - PYTHONUNBUFFERED=1
    command: >
      bash -c "
        python -u replay_socket_server.py samples/sample_1.dat --host 0.0.0.0 &
        python -u mock_ap_server.py --host 0.0.0.0 --port 8000
      "
    ports:
      - "8000:8000"   # AP HTTP interface  -> host:8000
      - "1333:1333"   # Tool data socket   -> host:1333
    volumes:
      - ./samples:/app/samples:ro
    restart: unless-stopped