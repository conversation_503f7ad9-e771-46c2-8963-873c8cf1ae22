#!/usr/bin/env python3
"""replay_socket_client.py

TCP client that connects to a server and sends binary tool data from a `.dat`/`.bin` 
file produced by `convert_msra_to_bin.py`. The client sends data sample-by-sample 
at (approximately) the original sample rate.

This is a modified version of replay_socket_server.py, converted from server mode
to client mode for testing with data_receiver_server.py.

Key differences from the original server version:
• Connects to a remote server instead of listening for connections
• Includes retry logic for connection failures
• Graceful handling of server disconnections
• Optional connection keep-alive and reconnection

Usage
-----
python replay_socket_client.py sample_1.dat [--host 127.0.0.1] [--port 1333] \
                                           [--sample-rate 10000] \
                                           [--block-samples 100] \
                                           [--no-throttle] \
                                           [--retry-interval 5] \
                                           [--max-retries 0]

Practical notes
---------------
The client will attempt to reconnect if the connection is lost. Set --max-retries 0
for infinite retries, or a positive number to limit retry attempts.
"""
from __future__ import annotations

import argparse
import pathlib
import socket
import time
import logging
import sys
from typing import List

try:
    import colorlog
    HAS_COLORLOG = True
except ImportError:
    HAS_COLORLOG = False

BYTES_PER_SAMPLE = 16
DEFAULT_SAMPLE_RATE = 10_000  # Hz
DEFAULT_BLOCK_SAMPLES = 100   # 100×16 = 1,600 B per send, ~10 ms at 10 kHz
DEFAULT_RETRY_INTERVAL = 5    # seconds


def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """Setup a logger with colored output if colorlog is available."""
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Avoid adding multiple handlers if logger already exists
    if logger.handlers:
        return logger

    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(level)

    # Setup formatter with colors if available
    if HAS_COLORLOG:
        formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

    handler.setFormatter(formatter)
    logger.addHandler(handler)

    return logger


# Initialize logger
logger = setup_logger('replay_socket_client')


def connect_to_server(host: str, port: int, retry_interval: float, max_retries: int) -> socket.socket:
    """Connect to the server with retry logic."""
    addr = (host, port)
    retry_count = 0
    
    while True:
        try:
            logger.info(f"Attempting to connect to {addr}...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(addr)
            logger.info(f"Successfully connected to {addr}")
            
            # Enable TCP keep-alive
            try:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                # Platform-specific tuning – ignore if not available
                if hasattr(socket, "TCP_KEEPIDLE"):
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 60)
                if hasattr(socket, "TCP_KEEPINTVL"):
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 10)
                if hasattr(socket, "TCP_KEEPCNT"):
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)
            except OSError:
                pass  # keep going even if keep-alive can't be set
            
            return sock
            
        except (socket.error, ConnectionRefusedError) as e:
            retry_count += 1
            logger.error(f"Connection failed: {e}")
            
            if max_retries > 0 and retry_count >= max_retries:
                logger.error(f"Max retries ({max_retries}) reached, giving up")
                raise ConnectionError(f"Could not connect to {addr} after {max_retries} attempts")
            
            logger.info(f"Retrying in {retry_interval} seconds... (attempt {retry_count})")
            time.sleep(retry_interval)


def send_data(
    data_path: pathlib.Path,
    host: str,
    port: int,
    sample_rate: float,
    block_samples: int,
    throttle: bool,
    retry_interval: float,
    max_retries: int,
) -> None:
    """Connect to server and send the data."""
    logger.info(f"Loading data from {data_path}...")
    
    data = data_path.read_bytes()
    if len(data) % BYTES_PER_SAMPLE:
        raise ValueError("Data length is not a multiple of 16-byte samples")

    total_samples = len(data) // BYTES_PER_SAMPLE
    logger.info(f"Total samples: {total_samples:,}")

    block_bytes = block_samples * BYTES_PER_SAMPLE
    blocks: List[memoryview] = [
        memoryview(data[i : i + block_bytes])
        for i in range(0, len(data), block_bytes)
    ]
    logger.info(
        f"Prepared {len(blocks)} blocks of {block_samples} samples "
        f"({block_bytes} bytes each)"
    )

    while True:
        try:
            sock = connect_to_server(host, port, retry_interval, max_retries)
            try:
                _stream_data(sock, blocks, sample_rate, block_samples, throttle)
                logger.info("Data transmission completed successfully")
                break  # Exit the retry loop on successful completion
            finally:
                sock.close()
                logger.info("Connection closed")
                
        except (BrokenPipeError, ConnectionResetError, socket.error) as e:
            logger.error(f"Connection lost during transmission: {e}")
            if max_retries > 0:
                logger.info("Attempting to reconnect...")
            else:
                logger.info("Attempting to reconnect (infinite retries)...")
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, stopping...")
            break
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            break


def _stream_data(
    sock: socket.socket,
    blocks: List[memoryview],
    sample_rate: float,
    block_samples: int,
    throttle: bool,
) -> None:
    """Send the blocks to the server; optionally throttle to real-time speed."""
    sleep_time = (block_samples / sample_rate) if throttle else 0.0
    
    logger.info(f"Starting data transmission...")
    if throttle:
        logger.info(f"Throttling enabled: {sleep_time*1000:.1f}ms sleep per block")
    else:
        logger.info("Throttling disabled: sending at maximum speed")
    
    start_time = time.time()
    bytes_sent = 0
    
    try:
        for cycle in range(1, 1000000):  # Limit cycles to prevent infinite loop
            for block_idx, blk in enumerate(blocks):
                sock.sendall(blk)
                bytes_sent += len(blk)
                
                if sleep_time:
                    time.sleep(sleep_time)
                
                # Log progress every 1000 blocks
                if (block_idx + 1) % 1000 == 0:
                    elapsed = time.time() - start_time
                    rate = bytes_sent / elapsed if elapsed > 0 else 0
                    logger.debug(
                        f"Sent {bytes_sent:,} bytes in {elapsed:.1f}s "
                        f"({rate/1024:.1f} KB/s)"
                    )
            
            logger.info(f"Completed cycle {cycle}, sent {bytes_sent:,} bytes total")
            
    except (BrokenPipeError, ConnectionResetError) as e:
        # Server closed the connection – this is expected behavior
        logger.info(f"Server closed connection: {e}")
        return


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    p = argparse.ArgumentParser(description="Send binary tool data to TCP server in real-time")
    p.add_argument("data", type=pathlib.Path, help=".dat/.bin file generated by convert_msra_to_bin.py")
    p.add_argument("--host", default="127.0.0.1", help="server hostname or IP (default 127.0.0.1)")
    p.add_argument("--port", type=int, default=1333, help="server TCP port (default 1333)")
    p.add_argument("--sample-rate", type=float, default=DEFAULT_SAMPLE_RATE, help="samples per second (default 10000)")
    p.add_argument(
        "--block-samples",
        type=int,
        default=DEFAULT_BLOCK_SAMPLES,
        help="number of samples per send() call (default 100)",
    )
    p.add_argument("--no-throttle", action="store_true", help="send data as fast as possible")
    p.add_argument(
        "--retry-interval",
        type=float,
        default=DEFAULT_RETRY_INTERVAL,
        help="seconds to wait between connection retries (default 5)"
    )
    p.add_argument(
        "--max-retries",
        type=int,
        default=0,
        help="maximum connection retry attempts (0 = infinite, default 0)"
    )
    p.add_argument("--debug", action="store_true", help="enable debug logging")
    return p.parse_args()


def main() -> None:
    """Main entry point."""
    args = parse_args()
    
    # Set log level based on debug flag
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    try:
        send_data(
            data_path=args.data,
            host=args.host,
            port=args.port,
            sample_rate=args.sample_rate,
            block_samples=args.block_samples,
            throttle=not args.no_throttle,
            retry_interval=args.retry_interval,
            max_retries=args.max_retries,
        )
    except Exception as e:
        logger.error(f"Client error: {e}")
        raise


if __name__ == "__main__":
    main()
